<?php

namespace App\Http\Controllers\Admin;

use App\Models\Classroom;
use App\Models\Program;
use App\Models\Shift;
use App\Models\Teacher;
use App\Models\AcademicYear;
use App\Enums\ActivityStatusEnum;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Requests\Requests\ClassroomRequests\ClassroomStoreRequest;
use App\Http\Requests\Requests\ClassroomRequests\ClassroomUpdateRequest;

class ClassroomController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->datatables($request);
        }

        return view('admin.classrooms.index', [
            'programs' => Program::where('activity_status', ActivityStatusEnum::Active)->get(),
            'shifts' => Shift::where('activity_status', ActivityStatusEnum::Active)->get(),
            'academicYears' => AcademicYear::all(),
            'statuses' => ActivityStatusEnum::options(),
        ]);
    }

    public function datatables($req)
    {
        $query = Classroom::with(['program', 'shift', 'homeroomTeacher.user', 'academicYear']);

        // Filter berdasarkan program
        if ($req->filled('program_id')) {
            $query->where('program_id', $req->program_id);
        }

        // Filter berdasarkan shift
        if ($req->filled('shift_id')) {
            $query->where('shift_id', $req->shift_id);
        }

        // Filter berdasarkan tahun akademik
        if ($req->filled('academic_year_id')) {
            $query->where('academic_year_id', $req->academic_year_id);
        }

        // Filter berdasarkan status
        if ($req->filled('activity_status')) {
            $query->where('activity_status', $req->activity_status);
        }

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('action', function ($row) {
                return view('admin.classrooms._action', compact('row'));
            })
            ->editColumn('activity_status', function ($row) {
                $color = $row->activity_status->color();
                $label = $row->activity_status->label();
                return "<span class='badge bg-{$color}-subtle text-{$color}'>{$label}</span>";
            })
            ->editColumn('program_name', function ($row) {
                return $row->program->name ?? '-';
            })
            ->editColumn('shift_name', function ($row) {
                return $row->shift->name ?? '-';
            })
            ->editColumn('homeroom_teacher', function ($row) {
                return $row->homeroomTeacher->user->name ?? '-';
            })
            ->editColumn('academic_year', function ($row) {
                return $row->academicYear->name ?? '-';
            })
            // Search global untuk semua kolom
            ->filter(function ($q) use ($req) {
                if ($search = $req->input('search.value')) {
                    $q->where(function ($sub) use ($search) {
                        $sub->where('code', 'like', "%{$search}%")
                            ->orWhere('name', 'like', "%{$search}%")
                            ->orWhere('level', 'like', "%{$search}%")
                            ->orWhereHas('program', function ($query) use ($search) {
                                $query->where('name', 'like', "%{$search}%");
                            })
                            ->orWhereHas('homeroomTeacher.user', function ($query) use ($search) {
                                $query->where('name', 'like', "%{$search}%");
                            });
                    });
                }
            })
            ->rawColumns(['action', 'activity_status'])
            ->toJson();
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.classrooms.create', [
            'programs' => Program::where('activity_status', ActivityStatusEnum::Active)->get(),
            'shifts' => Shift::where('activity_status', ActivityStatusEnum::Active)->get(),
            //tampilkan teacher yang belum ada di kelas lain
            'teachers' => Teacher::with('user')->whereDoesntHave('homeroomClassrooms')->get(),
            'academicYears' => AcademicYear::all(),
            'statuses' => ActivityStatusEnum::options(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(ClassroomStoreRequest $request)
    {
        // Mengambil data yang sudah divalidasi dari request.
        $data = $request->validated();

        // Menggunakan transaction untuk memastikan operasi berhasil atau tidak sama sekali.
        DB::transaction(function () use ($data) {
            // Buat kelas baru.
            Classroom::create($data);
        });

        return redirect()->route('admin.classrooms.index')
            ->with('success', 'Kelas berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        return view('admin.classrooms.edit', [
            'programs' => Program::where('activity_status', ActivityStatusEnum::Active)->get(),
            'shifts' => Shift::where('activity_status', ActivityStatusEnum::Active)->get(),
            //tampilkan teacher yang belum ada di kelas lain
            'teachers' => Teacher::with('user')->whereDoesntHave('homeroomClassrooms')->get(),
            'academicYears' => AcademicYear::all(),
            'statuses' => ActivityStatusEnum::options(),
            'classroom' => Classroom::findOrFail($id),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ClassroomUpdateRequest $request, string $id)
    {
        // Mengambil data yang sudah divalidasi dari request.
        $data = $request->validated();

        // Mencari data kelas berdasarkan ID.
        $classroom = Classroom::findOrFail($id);

        // Menggunakan transaction untuk memastikan operasi berhasil atau tidak sama sekali.
        DB::transaction(function () use ($classroom, $data) {
            // Update data kelas.
            $classroom->update($data);
        });

        return redirect()->route('admin.classrooms.index')
            ->with('success', 'Kelas berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $classroom = Classroom::findOrFail($id);

            // Cek apakah kelas memiliki relasi yang tidak boleh dihapus
            if ($classroom->students()->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Kelas tidak dapat dihapus karena masih memiliki siswa.'
                ], 400);
            }

            if ($classroom->teacherAssignments()->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Kelas tidak dapat dihapus karena masih memiliki penugasan guru.'
                ], 400);
            }

            $classroom->delete();

            return response()->json([
                'success' => true,
                'message' => 'Kelas berhasil dihapus.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus kelas.'
            ], 500);
        }
    }
}
