<?php

namespace Database\Seeders;

use App\Models\Configuration;
use Illuminate\Database\Seeder;

class ConfigurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $configurations = [
            [
                'name' => 'app_name',
                'value' => 'Rawooh MVC',
                'type' => 'string',
                'group' => 'system',
                'description' => 'Nama aplikasi yang ditampilkan di interface',
                'is_editable' => false,
            ],
            [
                'name' => 'app_version',
                'value' => '1.0.0',
                'type' => 'string',
                'group' => 'system',
                'description' => 'Versi aplikasi saat ini',
                'is_editable' => false,
            ],
            [
                'name' => 'maintenance_mode',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'system',
                'description' => 'Mode maintenance aplikasi',
                'is_editable' => true,
            ],
            [
                'name' => 'max_students_per_class',
                'value' => '30',
                'type' => 'integer',
                'group' => 'academic',
                'description' => 'Jumlah maksimal siswa per kelas',
                'is_editable' => true,
            ],
            [
                'name' => 'academic_year_start_month',
                'value' => '7',
                'type' => 'integer',
                'group' => 'academic',
                'description' => 'Bulan dimulainya tahun akademik (1-12)',
                'is_editable' => true,
            ],
            [
                'name' => 'attendance_tolerance_minutes',
                'value' => '15',
                'type' => 'integer',
                'group' => 'academic',
                'description' => 'Toleransi keterlambatan kehadiran dalam menit',
                'is_editable' => true,
            ],
            [
                'name' => 'enable_email_notifications',
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'notification',
                'description' => 'Aktifkan notifikasi email',
                'is_editable' => true,
            ],
            [
                'name' => 'enable_sms_notifications',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'notification',
                'description' => 'Aktifkan notifikasi SMS',
                'is_editable' => true,
            ],
            [
                'name' => 'session_timeout_minutes',
                'value' => '120',
                'type' => 'integer',
                'group' => 'security',
                'description' => 'Timeout session dalam menit',
                'is_editable' => true,
            ],
            [
                'name' => 'max_login_attempts',
                'value' => '5',
                'type' => 'integer',
                'group' => 'security',
                'description' => 'Maksimal percobaan login',
                'is_editable' => true,
            ],
        ];

        foreach ($configurations as $config) {
            Configuration::updateOrCreate(
                ['name' => $config['name']],
                $config
            );
        }
    }
}
