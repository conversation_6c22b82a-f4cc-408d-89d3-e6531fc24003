@extends('admin.layouts.app')

@section('title', 'Edit Guru')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Edit Guru',
        'breadcrumb' => 'Manajemen Guru',
    ])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">Edit Data Guru</h5>
                    </div>
                </div>

                <div class="card-body">
                    <form id="edit-form" method="POST" action="{{ route('admin.teachers.update', $teacher->id) }}">
                        @csrf
                        @method('PUT')

                        <div class="row g-3">
                            <div class="col-12">
                                <h5 class="text-primary">Informasi Akun</h5>
                                <hr>
                            </div>

                            <!-- <PERSON><PERSON> -->
                            <div class="col-md-6">
                                <label for="name" class="form-label"><PERSON><PERSON> <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name"
                                       value="{{ old('name', $teacher->user->name) }}" placeholder="Masukkan nama lengkap" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Username -->
                            <div class="col-md-6">
                                <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('username') is-invalid @enderror" id="username" name="username"
                                       value="{{ old('username', $teacher->user->username) }}" placeholder="Masukkan username" required>
                                @error('username')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Email -->
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email"
                                       value="{{ old('email', $teacher->user->email) }}" placeholder="Masukkan email" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Password -->
                            <div class="col-md-6">
                                <label for="password" class="form-label">Password <small class="text-muted">(Kosongkan jika tidak ingin mengubah)</small></label>
                                <div class="position-relative auth-pass-inputgroup">
                                    <input type="password" class="form-control @error('password') is-invalid @enderror pe-5" id="password" name="password"
                                           placeholder="Masukkan password baru">
                                    <button class="btn btn-link position-absolute end-0 top-0 text-decoration-none text-muted password-addon" type="button">
                                        <i class="ri-eye-fill align-middle"></i>
                                    </button>
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Nomor Telepon -->
                            <div class="col-md-6">
                                <label for="phone_number" class="form-label">Nomor Telepon</label>
                                <input type="text" class="form-control @error('phone_number') is-invalid @enderror" id="phone_number" name="phone_number"
                                       value="{{ old('phone_number', $teacher->user->phone_number) }}" placeholder="Masukkan nomor telepon">
                                @error('phone_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Status -->
                            <div class="col-md-6">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select @error('status') is-invalid @enderror" data-choices id="status" name="status">
                                    <option value="" disabled>Pilih Status</option>
                                    @foreach ($statuses as $value => $label)
                                        <option value="{{ $value }}" {{ old('status', $teacher->user->status->value) == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-12 mt-4">
                                <h5 class="text-primary">Informasi Guru</h5>
                                <hr>
                            </div>

                            <!-- Tempat Lahir -->
                            <div class="col-md-6">
                                <label for="birth_place" class="form-label">Tempat Lahir <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('birth_place') is-invalid @enderror" id="birth_place" name="birth_place"
                                       value="{{ old('birth_place', $teacher->birth_place) }}" placeholder="Masukkan tempat lahir" required>
                                @error('birth_place')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Tanggal Lahir -->
                            <div class="col-md-6">
                                <label for="birth_date" class="form-label">Tanggal Lahir <span class="text-danger">*</span></label>
                                <input type="text"
                                       class="form-control @error('birth_date') is-invalid @enderror"
                                       data-provider="flatpickr"
                                       data-date-format="d M Y"
                                       name="birth_date"
                                       value="{{ old('birth_date') ? \Carbon\Carbon::parse(old('birth_date'))->format('d M Y') : $teacher->birth_date->format('d M Y') }}"
                                       required>
                                @error('birth_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Jenis Kelamin -->
                            <div class="col-md-6">
                                <label for="gender" class="form-label">Jenis Kelamin <span class="text-danger">*</span></label>
                                <select class="form-select @error('gender') is-invalid @enderror" data-choices id="gender" name="gender" required>
                                    <option value="" disabled>Pilih Jenis Kelamin</option>
                                    @foreach ($genders as $value => $label)
                                        <option value="{{ $value }}" {{ old('gender', $teacher->gender->value) == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('gender')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Role Guru -->
                            <div class="col-md-6">
                                <label for="teacher_role" class="form-label">Role Guru <span class="text-danger">*</span></label>
                                <select class="form-select @error('teacher_role') is-invalid @enderror" data-choices id="teacher_role" name="teacher_role" required>
                                    <option value="" disabled>Pilih Role Guru</option>
                                    @foreach ($teacherRoles as $value => $label)
                                        <option value="{{ $value }}" {{ old('teacher_role', $teacher->teacher_role?->value) == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('teacher_role')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                        </div>

                        <!-- Tombol Aksi -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="hstack justify-content-end gap-2">
                                    <a href="{{ route('admin.teachers.index') }}" class="btn btn-ghost-secondary">
                                        <i class="ri-close-line align-bottom"></i> Batal
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="submit-btn">
                                        <i class="ri-save-line align-bottom"></i> Perbarui
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // Toggle password visibility
        document.querySelector('.password-addon').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            this.querySelector('i').classList.toggle('ri-eye-fill');
            this.querySelector('i').classList.toggle('ri-eye-off-fill');
        });
    </script>
@endpush
