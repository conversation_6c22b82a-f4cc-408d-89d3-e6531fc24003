@extends('admin.layouts.app')

@section('title', 'Manajemen Kelas')

@section('content')
    @include('admin.components.page-title', ['title' => 'Manajemen Kelas', 'breadcrumb' => 'Manajemen Akademik'])

    <div class="row">
        <div class="col-lg-12">
            <div class="card" id="tasksList">
                <!-- Start Header -->
                <div class="card-header border-0">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">Kelas
                            <span class="badge bg-primary-subtle text-primary ms-2" id="total-classrooms">0</span>
                        </h5>
                        <div class="flex-shrink-0">
                            <div class="d-flex flex-wrap gap-2">
                                <a href="{{ route('admin.classrooms.create') }}" class="btn btn-primary">
                                    <i class="ri-add-line align-bottom me-1"></i> <PERSON><PERSON>las
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- End Header -->

                <!-- Start Filter Section -->
                <div class="card-body border-bottom-dashed border-bottom">
                    <form>
                        <div class="row g-3">
                            <!-- Filter Program -->
                            <div class="col-xl-3 col-sm-6">
                                <div class="search-box">
                                    <select class="form-control" data-choices name="program_id" id="program_id">
                                        <option value="">Semua Program</option>
                                        @foreach ($programs as $program)
                                            <option value="{{ $program->id }}">{{ $program->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <!-- Filter Shift -->
                            <div class="col-xl-3 col-sm-6">
                                <div class="search-box">
                                    <select class="form-control" data-choices name="shift_id" id="shift_id">
                                        <option value="">Semua Shift</option>
                                        @foreach ($shifts as $shift)
                                            <option value="{{ $shift->id }}">{{ $shift->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <!-- Filter Tahun Akademik -->
                            <div class="col-xl-3 col-sm-6">
                                <div class="search-box">
                                    <select class="form-control" data-choices name="academic_year_id" id="academic_year_id">
                                        <option value="">Semua Tahun Akademik</option>
                                        @foreach ($academicYears as $academicYear)
                                            <option value="{{ $academicYear->id }}">{{ $academicYear->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <!-- Filter Status -->
                            <div class="col-xl-3 col-sm-6">
                                <div class="search-box">
                                    <select class="form-control" data-choices name="activity_status" id="activity_status">
                                        <option value="">Semua Status</option>
                                        @foreach ($statuses as $value => $label)
                                            <option value="{{ $value }}">{{ $label }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- End Filter Section -->

                <!-- Start Table Section -->
                <div class="card-body">
                    <div class="table-responsive table-card">
                        <table id="classrooms-table" class="table align-middle table-nowrap" style="width: 100%;">
                            <thead class="table-light text-muted">
                                <tr>
                                    <th scope="col" style="width: 65px;">
                                        No
                                    </th>
                                    <th>Kode</th>
                                    <th>Nama Kelas</th>
                                    <th>Tingkat</th>
                                    <th>Kapasitas</th>
                                    <th>Program</th>
                                    <th>Shift</th>
                                    <th>Wali Kelas</th>
                                    <th>Tahun Akademik</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                <!-- End Table Section -->

            </div>
            <!--end card-->
        </div>
        <!--end col-->
    </div>
    <!--end row-->
@endsection

@include('admin.partials.plugins._jquery')
@include('admin.partials.plugins._datatables')

@push('scripts')
    <script>
        $(document).ready(function() {
            var table = $('#classrooms-table').DataTable({
                lengthChange: false,
                searching: false,
                ordering: false,
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('admin.classrooms.index') }}",
                    data: function(d) {
                        d.program_id = $('#program_id').val();
                        d.shift_id = $('#shift_id').val();
                        d.academic_year_id = $('#academic_year_id').val();
                        d.activity_status = $('#activity_status').val();
                    }
                },
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'code',
                        name: 'code'
                    },
                    {
                        data: 'name',
                        name: 'name'
                    },
                    {
                        data: 'level',
                        name: 'level'
                    },
                    {
                        data: 'capacity',
                        name: 'capacity'
                    },
                    {
                        data: 'program_name',
                        name: 'program.name'
                    },
                    {
                        data: 'shift_name',
                        name: 'shift.name'
                    },
                    {
                        data: 'homeroom_teacher',
                        name: 'homeroomTeacher.user.name'
                    },
                    {
                        data: 'academic_year',
                        name: 'academicYear.name'
                    },
                    {
                        data: 'activity_status',
                        name: 'activity_status'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false
                    }
                ],
                drawCallback: function(settings) {
                    var api = this.api();
                    var recordsTotal = api.page.info().recordsTotal;
                    $('#total-classrooms').text(recordsTotal);
                }
            });

            // Filter change events
            $('#program_id, #shift_id, #academic_year_id, #activity_status').change(function() {
                table.draw();
            });

            // Delete functionality
            table.on('click', '.btn-delete', function() {
                var id = $(this).data('id');
                var name = $(this).data('name');

                Swal.fire({
                    title: 'Apakah Anda yakin?',
                    text: `Kelas "${name}" akan dihapus permanen!`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Ya, hapus!',
                    cancelButtonText: 'Batal'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: `/admin/classrooms/${id}`,
                            type: 'DELETE',
                            data: {
                                _token: '{{ csrf_token() }}'
                            },
                            success: function(response) {
                                if (response.success) {
                                    Swal.fire('Berhasil!', response.message, 'success');
                                    table.ajax.reload();
                                } else {
                                    Swal.fire('Gagal!', response.message, 'error');
                                }
                            },
                            error: function(xhr) {
                                var response = JSON.parse(xhr.responseText);
                                Swal.fire('Gagal!', response.message, 'error');
                            }
                        });
                    }
                });
            });
        });
    </script>
@endpush
