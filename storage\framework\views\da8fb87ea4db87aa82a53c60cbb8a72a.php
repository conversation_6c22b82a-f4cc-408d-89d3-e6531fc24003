<?php $__env->startSection('title', 'Guru'); ?>

<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('admin.components.page-title', ['title' => 'Guru', 'breadcrumb' => 'Guru'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card" id="tasksList">
                <!-- Start Header -->
                <div class="card-header border-0">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">Guru
                            <span class="badge bg-primary-subtle text-primary ms-2" id="total-teachers">0</span>
                        </h5>
                        <div class="flex-shrink-0">
                            <div class="d-flex flex-wrap gap-2">
                                <a href="<?php echo e(route('admin.teachers.create')); ?>" class="btn btn-primary">
                                    <i class="ri-add-line align-bottom me-1"></i> Tambah Guru
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- End Header -->

                <!-- Start Filter Section -->
                <div class="card-body border-bottom-dashed border-bottom">
                    <form id="filter-form" class="row g-3">
                        <div class="col-md-3">
                            <label for="filter-status" class="form-label">Status</label>
                            <select class="form-select" data-choices name="status" id="filter-status">
                                <option value="">Semua Status</option>
                                <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($value); ?>"><?php echo e($label); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="filter-role" class="form-label">Role</label>
                            <select class="form-select" data-choices name="role" id="filter-role">
                                <option value="">Semua Role</option>
                                <?php $__currentLoopData = $teacherRoles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($value); ?>"><?php echo e($label); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="filter-gender" class="form-label">Jenis Kelamin</label>
                            <select class="form-select" data-choices name="gender" id="filter-gender">
                                <option value="">Semua Jenis Kelamin</option>
                                <?php $__currentLoopData = $genders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($value); ?>"><?php echo e($label); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="search-input" class="form-label">Cari</label>
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Cari nama, email, tempat lahir..." id="search-input" name="search">
                                <button class="btn btn-primary" type="button" id="search-button">
                                    <i class="ri-search-line"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- End Filter Section -->

                <!-- Start Table Section -->
                <div class="card-body">
                    <div class="table-responsive table-card">
                        <table id="teachers-table" class="table align-middle table-nowrap" style="width: 100%;">
                            <thead class="table-light text-muted">
                                <tr>
                                    <th scope="col" style="width: 65px;">
                                        No
                                    </th>
                                    <th>Nama</th>
                                    <th>Email</th>
                                    <th>No. Telepon</th>
                                    <th>Jenis Kelamin</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                <!-- End Table Section -->

            </div>
            <!--end card-->
        </div>
        <!--end col-->
    </div>
    <!--end row-->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.partials.plugins._jquery', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('admin.partials.plugins._datatables', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            const table = $('#teachers-table').DataTable({
                lengthChange: false,
                searching: false,
                ordering: false,
                processing: true,
                serverSide: true,
                ajax: {
                    url: "<?php echo e(route('admin.teachers.index')); ?>",
                    data: function(d) {
                        d.columns[4].search.value = $('#filter-gender').val(); // gender
                        d.columns[5].search.value = $('#filter-role').val(); // teacher_role
                        d.columns[6].search.value = $('#filter-status').val(); // status
                        d.search.value = $('#search-input').val(); // search
                    },
                    complete: response => {
                        const totalRecords = response.responseJSON?.recordsTotal || 0;
                        $('#total-teachers').text(totalRecords);
                    },
                },
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'name',
                        name: 'name'
                    },
                    {
                        data: 'email',
                        name: 'email'
                    },
                    {
                        data: 'phone_number',
                        name: 'phone_number'
                    },
                    {
                        data: 'gender',
                        name: 'gender'
                    },
                    {
                        data: 'teacher_role',
                        name: 'teacher_role'
                    },
                    {
                        data: 'status',
                        name: 'status'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false
                    },
                ],
                language: {
                    processing: "Memproses...",
                    lengthMenu: "Tampilkan _MENU_ data",
                    zeroRecords: "Data tidak ditemukan",
                    info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                    infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                    infoFiltered: "(disaring dari _MAX_ total data)",
                    search: "Cari:",
                    paginate: {
                        first: "Pertama",
                        last: "Terakhir",
                        next: "Selanjutnya",
                        previous: "Sebelumnya"
                    }
                }
            });

            // Search functionality
            $('#search-button').on('click', function() {
                table.draw();
            });

            $('#search-input').on('keyup', function(e) {
                if (e.keyCode === 13) {
                    table.draw();
                }
            });

            // Filter functionality
            $('#filter-gender, #filter-role, #filter-status').on('change', function() {
                table.draw();
            });

            // Delete functionality
            table.on('click', '.btn-delete', function() {
                const url = $(this).data('url');
                const name = $(this).data('name');

                Swal.fire({
                    title: 'Apakah Anda yakin?',
                    text: `Data guru "${name}" akan dihapus permanen!`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Ya, Hapus!',
                    cancelButtonText: 'Batal'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: url,
                            type: 'DELETE',
                            data: {
                                _token: '<?php echo e(csrf_token()); ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    Swal.fire('Berhasil!', response.message, 'success');
                                    table.ajax.reload();
                                } else {
                                    Swal.fire('Error!', response.message, 'error');
                                }
                            },
                            error: function(xhr) {
                                Swal.fire('Error!', 'Terjadi kesalahan saat menghapus data.', 'error');
                            }
                        });
                    }
                });
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\rawooh-mvc-optimize\resources\views/admin/teachers/index.blade.php ENDPATH**/ ?>