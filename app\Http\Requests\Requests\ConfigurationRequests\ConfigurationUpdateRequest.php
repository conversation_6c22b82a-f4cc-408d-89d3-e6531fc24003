<?php

namespace App\Http\Requests\Requests\ConfigurationRequests;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class ConfigurationUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255', Rule::unique('configurations')->ignore($this->route('configuration'))],
            'value' => ['required', 'string'],
            'type' => ['required', 'string', 'max:255', Rule::in(['string', 'integer', 'boolean', 'json'])],
            'group' => ['nullable', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'is_editable' => ['boolean'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Nama konfigurasi harus diisi',
            'name.string' => 'Nama konfigurasi harus berupa string',
            'name.max' => 'Nama konfigurasi maksimal 255 karakter',
            'name.unique' => 'Nama konfigurasi sudah ada',
            'value.required' => 'Nilai konfigurasi harus diisi',
            'value.string' => 'Nilai konfigurasi harus berupa string',
            'type.required' => 'Tipe konfigurasi harus diisi',
            'type.string' => 'Tipe konfigurasi harus berupa string',
            'type.max' => 'Tipe konfigurasi maksimal 255 karakter',
            'type.in' => 'Tipe konfigurasi harus berupa string, integer, boolean, atau json',
            'group.string' => 'Grup konfigurasi harus berupa string',
            'group.max' => 'Grup konfigurasi maksimal 255 karakter',
            'description.string' => 'Deskripsi konfigurasi harus berupa string',
            'is_editable.boolean' => 'Status dapat diedit harus berupa boolean',
        ];
    }
}
