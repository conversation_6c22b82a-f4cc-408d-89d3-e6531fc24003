@extends('admin.layouts.app')

@section('title', 'Edit Kon<PERSON>gu<PERSON>i')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Edit Konfigurasi',
        'breadcrumb' => 'Manajemen Sistem',
    ])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">Edit Konfigurasi</h5>
                    </div>
                </div>

                <div class="card-body">
                    <form id="edit-form" method="POST" action="{{ route('admin.configurations.update', $configuration->id) }}">
                        @csrf
                        @method('PUT')

                        <div class="row g-3">
                            <!-- <PERSON>a Konfigurasi -->
                            <div class="col-md-6">
                                <label for="name" class="form-label"><PERSON><PERSON> <span class="text-danger">*</span></label>
                                <input type="text"
                                       class="form-control @error('name') is-invalid @enderror"
                                       id="name" name="name"
                                       value="{{ old('name', $configuration->name) }}"
                                       placeholder="contoh: app_name, max_students" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Tipe -->
                            <div class="col-md-6">
                                <label for="type" class="form-label">Tipe <span class="text-danger">*</span></label>
                                <select class="form-select @error('type') is-invalid @enderror"
                                        data-choices id="type" name="type" required>
                                    <option value="" disabled>Pilih Tipe</option>
                                    @foreach ($types as $value => $label)
                                        <option value="{{ $value }}"
                                                {{ old('type', $configuration->type) == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Nilai -->
                            <div class="col-md-6">
                                <label for="value" class="form-label">Nilai <span class="text-danger">*</span></label>
                                @if ($configuration->type === 'boolean')
                                    <select class="form-control @error('value') is-invalid @enderror" id="value" name="value" required>
                                        <option value="true" {{ old('value', $configuration->value) == 'true' ? 'selected' : '' }}>Ya</option>
                                        <option value="false" {{ old('value', $configuration->value) == 'false' ? 'selected' : '' }}>Tidak</option>
                                    </select>
                                @elseif($configuration->type === 'integer')
                                    <input type="number" class="form-control @error('value') is-invalid @enderror" id="value" name="value"
                                           value="{{ old('value', $configuration->value) }}" required>
                                @else
                                    <textarea class="form-control @error('value') is-invalid @enderror" id="value" name="value" rows="3"
                                              placeholder="Masukkan nilai konfigurasi" required>{{ old('value', $configuration->value) }}</textarea>
                                @endif
                                @error('value')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Grup -->
                            <div class="col-md-6">
                                <label for="group" class="form-label">Grup</label>
                                <select class="form-control @error('group') is-invalid @enderror" id="group" name="group">
                                    <option value="" disabled>Pilih Grup</option>
                                    @foreach ($groups as $value => $label)
                                        <option value="{{ $value }}"
                                                {{ old('group', $configuration->group) == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('group')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Deskripsi -->
                            <div class="col-md-8">
                                <label for="description" class="form-label">Deskripsi</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="3"
                                          placeholder="Masukkan deskripsi konfigurasi">{{ old('description', $configuration->description) }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Dapat Diedit -->
                            <div class="col-md-4">
                                <label class="form-label">Pengaturan</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_editable" name="is_editable" value="1"
                                           {{ old('is_editable', $configuration->is_editable) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_editable">
                                        Dapat Diedit
                                    </label>
                                </div>
                                <small class="form-text text-muted">Konfigurasi yang tidak dapat diedit tidak bisa dihapus</small>
                            </div>
                        </div>

                        <!-- Tombol Aksi -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="hstack justify-content-end gap-2">
                                    <a href="{{ route('admin.configurations.index') }}" class="btn btn-ghost-secondary">
                                        <i class="ri-close-line align-bottom"></i> Batal
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="submit-btn">
                                        <i class="ri-save-line align-bottom"></i> Perbarui
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function() {
            // Handle type change to show appropriate input
            $('#type').change(function() {
                const type = $(this).val();
                const valueField = $('#value');
                const currentValue = '{{ $configuration->value }}';

                if (type === 'boolean') {
                    const selectedValue = currentValue === 'true' ? 'true' : 'false';
                    valueField.replaceWith('<select class="form-control" id="value" name="value" required><option value="true" ' + (selectedValue === 'true' ? 'selected' : '') + '>Ya</option><option value="false" ' + (selectedValue === 'false' ? 'selected' : '') + '>Tidak</option></select>');
                } else if (type === 'integer') {
                    valueField.replaceWith('<input type="number" class="form-control" id="value" name="value" value="' + currentValue + '" required>');
                } else {
                    valueField.replaceWith('<textarea class="form-control" id="value" name="value" rows="3" required>' + currentValue + '</textarea>');
                }
            });
        });
    </script>
@endpush
