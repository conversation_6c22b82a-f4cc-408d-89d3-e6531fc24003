<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Buat 1 Admin
        $admin = User::create([
            'username' => 'admin123',
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
            'phone_number' => '081234567888',
            'status' => 'active',
        ]);
        $admin->assignRole('admin');

        // Buat 1 Principal
        $principal = User::create([
            'username' => 'principal123',
            'name' => 'Principal User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
            'phone_number' => '081234567890',
            'status' => 'active',
        ]);
        $principal->assignRole('principal');

        // Buat 1 Treasurer
        $treasurer = User::create([
            'username' => 'treasurer123',
            'name' => 'Treasurer User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
            'phone_number' => '081234567891',
            'status' => 'active',
        ]);
        $treasurer->assignRole('treasurer');
    }
}
