<?php

namespace Database\Seeders;

use App\Models\Classroom;
use App\Models\Program;
use App\Models\Shift;
use App\Models\AcademicYear;
use App\Enums\ActivityStatusEnum;
use Illuminate\Database\Seeder;

class ClassroomSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ambil data yang diperlukan
        $academicYear = AcademicYear::first();
        $programs = Program::all();
        $shifts = Shift::all();

        if (!$academicYear || $programs->isEmpty() || $shifts->isEmpty()) {
            $this->command->warn('Data prerequisite (Academic Year, Programs, Shifts) belum ada. Jalankan seeder lain terlebih dahulu.');
            return;
        }

        $classrooms = [
            // Program Unggulan
            [
                'code' => 'X-UNG-1',
                'name' => 'Kelas X Unggulan 1',
                'level' => 'X',
                'capacity' => 30,
                'program_id' => $programs->where('code', 'UNG')->first()->id,
                'shift_id' => $shifts->where('name', 'Pagi')->first()->id,
                'teacher_id' => null, // Akan diisi setelah ada data teacher
                'academic_year_id' => $academicYear->id,
                'activity_status' => ActivityStatusEnum::Active,
                'description' => 'Kelas X Program Unggulan 1',
            ],
            [
                'code' => 'X-UNG-2',
                'name' => 'Kelas X Unggulan 2',
                'level' => 'X',
                'capacity' => 30,
                'program_id' => $programs->where('code', 'UNG')->first()->id,
                'shift_id' => $shifts->where('name', 'Pagi')->first()->id,
                'teacher_id' => null,
                'academic_year_id' => $academicYear->id,
                'activity_status' => ActivityStatusEnum::Active,
                'description' => 'Kelas X Program Unggulan 2',
            ],
            [
                'code' => 'XI-UNG-1',
                'name' => 'Kelas XI Unggulan 1',
                'level' => 'XI',
                'capacity' => 28,
                'program_id' => $programs->where('code', 'UNG')->first()->id,
                'shift_id' => $shifts->where('name', 'Pagi')->first()->id,
                'teacher_id' => null,
                'academic_year_id' => $academicYear->id,
                'activity_status' => ActivityStatusEnum::Active,
                'description' => 'Kelas XI Program Unggulan 1',
            ],
            [
                'code' => 'XII-UNG-1',
                'name' => 'Kelas XII Unggulan 1',
                'level' => 'XII',
                'capacity' => 25,
                'program_id' => $programs->where('code', 'UNG')->first()->id,
                'shift_id' => $shifts->where('name', 'Pagi')->first()->id,
                'teacher_id' => null,
                'academic_year_id' => $academicYear->id,
                'activity_status' => ActivityStatusEnum::Active,
                'description' => 'Kelas XII Program Unggulan 1',
            ],

            // Program Reguler
            [
                'code' => 'X-REG-1',
                'name' => 'Kelas X Reguler 1',
                'level' => 'X',
                'capacity' => 32,
                'program_id' => $programs->where('code', 'REG')->first()->id,
                'shift_id' => $shifts->where('name', 'Pagi')->first()->id,
                'teacher_id' => null,
                'academic_year_id' => $academicYear->id,
                'activity_status' => ActivityStatusEnum::Active,
                'description' => 'Kelas X Program Reguler 1',
            ],
            [
                'code' => 'X-REG-2',
                'name' => 'Kelas X Reguler 2',
                'level' => 'X',
                'capacity' => 32,
                'program_id' => $programs->where('code', 'REG')->first()->id,
                'shift_id' => $shifts->where('name', 'Siang')->first()->id,
                'teacher_id' => null,
                'academic_year_id' => $academicYear->id,
                'activity_status' => ActivityStatusEnum::Active,
                'description' => 'Kelas X Program Reguler 2',
            ],
            [
                'code' => 'XI-REG-1',
                'name' => 'Kelas XI Reguler 1',
                'level' => 'XI',
                'capacity' => 30,
                'program_id' => $programs->where('code', 'REG')->first()->id,
                'shift_id' => $shifts->where('name', 'Pagi')->first()->id,
                'teacher_id' => null,
                'academic_year_id' => $academicYear->id,
                'activity_status' => ActivityStatusEnum::Active,
                'description' => 'Kelas XI Program Reguler 1',
            ],
            [
                'code' => 'XII-REG-1',
                'name' => 'Kelas XII Reguler 1',
                'level' => 'XII',
                'capacity' => 28,
                'program_id' => $programs->where('code', 'REG')->first()->id,
                'shift_id' => $shifts->where('name', 'Pagi')->first()->id,
                'teacher_id' => null,
                'academic_year_id' => $academicYear->id,
                'activity_status' => ActivityStatusEnum::Active,
                'description' => 'Kelas XII Program Reguler 1',
            ],

            // Program Tahfidz
            [
                'code' => 'X-TAH-1',
                'name' => 'Kelas X Tahfidz 1',
                'level' => 'X',
                'capacity' => 25,
                'program_id' => $programs->where('code', 'TAH')->first()->id,
                'shift_id' => $shifts->where('name', 'Pagi')->first()->id,
                'teacher_id' => null,
                'academic_year_id' => $academicYear->id,
                'activity_status' => ActivityStatusEnum::Active,
                'description' => 'Kelas X Program Tahfidz 1',
            ],
            [
                'code' => 'XI-TAH-1',
                'name' => 'Kelas XI Tahfidz 1',
                'level' => 'XI',
                'capacity' => 22,
                'program_id' => $programs->where('code', 'TAH')->first()->id,
                'shift_id' => $shifts->where('name', 'Pagi')->first()->id,
                'teacher_id' => null,
                'academic_year_id' => $academicYear->id,
                'activity_status' => ActivityStatusEnum::Active,
                'description' => 'Kelas XI Program Tahfidz 1',
            ],

            // Kelas tidak aktif untuk testing
            [
                'code' => 'X-REG-3',
                'name' => 'Kelas X Reguler 3 (Tidak Aktif)',
                'level' => 'X',
                'capacity' => 30,
                'program_id' => $programs->where('code', 'REG')->first()->id,
                'shift_id' => $shifts->where('name', 'Siang')->first()->id,
                'teacher_id' => null,
                'academic_year_id' => $academicYear->id,
                'activity_status' => ActivityStatusEnum::Inactive,
                'description' => 'Kelas X Program Reguler 3 yang tidak aktif',
            ],
        ];

        foreach ($classrooms as $classroom) {
            Classroom::create($classroom);
        }
    }
}
