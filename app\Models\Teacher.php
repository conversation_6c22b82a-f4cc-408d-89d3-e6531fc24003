<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Enums\GenderEnum;
use App\Enums\TeacherRoleEnum;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Teacher extends Model
{
    use HasFactory;

    protected $fillable = [
        'birth_place',
        'birth_date',
        'gender',
        'user_id',
    ];

    protected $casts = [
        'gender' => GenderEnum::class,
        'birth_date' => 'date',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function homeroomClassrooms(): HasMany
    {
        return $this->hasMany(Classroom::class);
    }

    public function teacherAssignments(): HasMany
    {
        return $this->hasMany(TeacherAssignment::class);
    }

    public function leaveRequests(): HasMany
    {
        return $this->hasMany(LeaveRequest::class);
    }

    public function journals(): HasMany
    {
        return $this->hasMany(Journal::class);
    }

    public function studentAttendances(): HasMany
    {
        return $this->hasMany(StudentAttendance::class);
    }

    public function teacherAttendances(): Has<PERSON><PERSON>
    {
        return $this->hasMany(TeacherAttendance::class);
    }

    /**
     * Get teacher role from user roles
     */
    public function getTeacherRoleAttribute()
    {
        if (!$this->user) {
            return null;
        }

        if ($this->user->hasRole('subject_teacher')) {
            return TeacherRoleEnum::SubjectTeacher;
        }

        if ($this->user->hasRole('substitute_teacher')) {
            return TeacherRoleEnum::SubstituteTeacher;
        }

        return null;
    }

    /**
     * Check if teacher has specific role
     */
    public function hasTeacherRole(string $role): bool
    {
        return $this->user && $this->user->hasRole($role);
    }

    /**
     * Get teacher role label
     */
    public function getTeacherRoleLabelAttribute(): string
    {
        $role = $this->teacher_role;
        return $role ? $role->label() : '-';
    }
}
