<?php

namespace App\Http\Requests\Requests\SubjectRequests;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Enums\ActivityStatusEnum;

class SubjectUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'code' => ['required', 'string', 'max:50', Rule::unique('subjects', 'code')->ignore($this->route('subject'))],
            'name' => ['required', 'string', 'max:255'],
            'program_id' => ['required', 'exists:programs,id'],
            'activity_status' => ['required', 'string', 'max:255', Rule::enum(ActivityStatusEnum::class)],
        ];
    }

    public function messages(): array
    {
        return [
            'code.required' => 'Kode mata pelajaran harus diisi',
            'code.string' => 'Kode mata pelajaran harus berupa string',
            'code.max' => 'Kode mata pelajaran maksimal 50 karakter',
            'code.unique' => 'Kode mata pelajaran sudah digunakan',
            'name.required' => 'Nama mata pelajaran harus diisi',
            'name.string' => 'Nama mata pelajaran harus berupa string',
            'name.max' => 'Nama mata pelajaran maksimal 255 karakter',
            'program_id.required' => 'Program harus dipilih',
            'program_id.exists' => 'Program yang dipilih tidak valid',
            'activity_status.required' => 'Status harus diisi',
            'activity_status.string' => 'Status harus berupa string',
            'activity_status.max' => 'Status maksimal 255 karakter',
            'activity_status.enum' => 'Status harus berupa aktif atau tidak aktif',
        ];
    }
}
