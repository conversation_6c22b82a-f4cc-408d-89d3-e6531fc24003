<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Configuration extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'value',
        'type',
        'group',
        'description',
        'is_editable',
    ];

    protected $casts = [
        'is_editable' => 'boolean',
        'value' => 'string',
    ];

    /**
     * Get configuration value by name
     */
    public static function getValue(string $name, $default = null)
    {
        $config = static::where('name', $name)->first();
        return $config ? $config->value : $default;
    }

    /**
     * Set configuration value by name
     */
    public static function setValue(string $name, $value, string $type = 'string', string $group = null, string $description = null)
    {
        return static::updateOrCreate(
            ['name' => $name],
            [
                'value' => $value,
                'type' => $type,
                'group' => $group,
                'description' => $description,
            ]
        );
    }
}
