<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Enums\ActivityStatusEnum;

class Subject extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'program_id',
        'activity_status',
    ];

    protected $casts = [
        'activity_status' => ActivityStatusEnum::class,
    ];

    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    public function teacherAssignments(): HasMany
    {
        return $this->hasMany(TeacherAssignment::class);
    }
}
