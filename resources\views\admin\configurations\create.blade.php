@extends('admin.layouts.app')

@section('title', 'Tambah Konfigurasi')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Tambah Konfigurasi',
        'breadcrumb' => 'Manajemen Sistem',
    ])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">Tambah Konfigurasi Baru</h5>
                    </div>
                </div>

                <div class="card-body">
                    <form id="create-form" method="POST" action="{{ route('admin.configurations.store') }}">
                        @csrf

                        <div class="row g-3">
                            <!-- <PERSON><PERSON>n<PERSON>gurasi -->
                            <div class="col-md-6">
                                <label for="name" class="form-label"><PERSON><PERSON> <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name"
                                       value="{{ old('name') }}" placeholder="contoh: app_name, max_students" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Tipe -->
                            <div class="col-md-6">
                                <label for="type" class="form-label">Tipe <span class="text-danger">*</span></label>
                                <select class="form-select @error('type') is-invalid @enderror" data-choices id="type" name="type" required>
                                    <option value="" selected disabled>Pilih Tipe</option>
                                    @foreach ($types as $value => $label)
                                        <option value="{{ $value }}" {{ old('type') == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Nilai -->
                            <div class="col-md-6">
                                <label for="value" class="form-label">Nilai <span class="text-danger">*</span></label>
                                <textarea class="form-control @error('value') is-invalid @enderror" id="value" name="value" rows="3"
                                          placeholder="Masukkan nilai konfigurasi" required>{{ old('value') }}</textarea>
                                @error('value')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Grup -->
                            <div class="col-md-6">
                                <label for="group" class="form-label">Grup</label>
                                <select class="form-select @error('group') is-invalid @enderror" data-choices id="group" name="group">
                                    <option value="" selected disabled>Pilih Grup</option>
                                    @foreach ($groups as $value => $label)
                                        <option value="{{ $value }}" {{ old('group') == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('group')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Deskripsi -->
                            <div class="col-md-8">
                                <label for="description" class="form-label">Deskripsi</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="3"
                                          placeholder="Masukkan deskripsi konfigurasi">{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Dapat Diedit -->
                            <div class="col-md-4">
                                <label class="form-label">Pengaturan</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_editable" name="is_editable" value="1" {{ old('is_editable', true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_editable">
                                        Dapat Diedit
                                    </label>
                                </div>
                                <small class="form-text text-muted">Konfigurasi yang tidak dapat diedit tidak bisa dihapus</small>
                            </div>
                        </div>

                        <!-- Tombol Aksi -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="hstack justify-content-end gap-2">
                                    <a href="{{ route('admin.configurations.index') }}" class="btn btn-ghost-secondary">
                                        <i class="ri-close-line align-bottom"></i> Batal
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="submit-btn">
                                        <i class="ri-save-line align-bottom"></i> Simpan
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function() {
            // Handle type change to show appropriate input
            $('#type').change(function() {
                const type = $(this).val();
                const valueField = $('#value');
                const currentValue = valueField.val();

                if (type === 'boolean') {
                    valueField.replaceWith('<select class="form-control" id="value" name="value" required><option value="true">Ya</option><option value="false">Tidak</option></select>');
                    $('#value').val(currentValue || 'true');
                } else if (type === 'integer') {
                    valueField.replaceWith('<input type="number" class="form-control" id="value" name="value" value="' + (currentValue || '') + '" required>');
                } else {
                    valueField.replaceWith('<textarea class="form-control" id="value" name="value" rows="3" required>' + (currentValue || '') + '</textarea>');
                }
            });
        });
    </script>
@endpush
