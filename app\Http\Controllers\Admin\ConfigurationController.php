<?php

namespace App\Http\Controllers\Admin;

use App\Models\Configuration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Requests\Requests\ConfigurationRequests\ConfigurationStoreRequest;
use App\Http\Requests\Requests\ConfigurationRequests\ConfigurationUpdateRequest;

class ConfigurationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->datatables($request);
        }

        return view('admin.configurations.index', [
            'types' => [
                'string' => 'String',
                'integer' => 'Integer',
                'boolean' => 'Boolean',
                'json' => 'JSON',
            ],
            'groups' => [
                'system' => 'System',
                'academic' => 'Academic',
                'notification' => 'Notification',
                'general' => 'General',
            ],
        ]);
    }

    public function datatables($req)
    {
        $query = Configuration::query();

        return DataTables::eloquent($query)
            ->addIndexColumn()
            ->editColumn('type', function ($row) {
                $typeColors = [
                    'string' => 'primary',
                    'integer' => 'info',
                    'boolean' => 'warning',
                    'json' => 'success',
                ];
                $color = $typeColors[$row->type] ?? 'secondary';
                return '<span class="badge bg-' . $color . ' text-white">' . ucfirst($row->type) . '</span>';
            })
            ->editColumn('is_editable', function ($row) {
                $color = $row->is_editable ? 'success' : 'danger';
                $text = $row->is_editable ? 'Ya' : 'Tidak';
                return '<span class="badge bg-' . $color . ' text-white">' . $text . '</span>';
            })
            ->editColumn('value', function ($row) {
                if ($row->type === 'boolean') {
                    return $row->value ? 'Ya' : 'Tidak';
                }
                if (strlen($row->value) > 50) {
                    return substr($row->value, 0, 50) . '...';
                }
                return $row->value;
            })
            ->addColumn('action', function ($row) {
                return view('admin.configurations._action', [
                    'edit' => route('admin.configurations.edit', $row->id),
                    'destroy' => route('admin.configurations.destroy', $row->id),
                    'id' => $row->id,
                    'is_editable' => $row->is_editable,
                ]);
            })
            // Filter kolom type
            ->filterColumn('type', function ($q, $keyword) {
                if (!empty($keyword) && $keyword !== 'all') {
                    $q->where('type', $keyword);
                }
            })
            // Filter kolom group
            ->filterColumn('group', function ($q, $keyword) {
                if (!empty($keyword) && $keyword !== 'all') {
                    $q->where('group', $keyword);
                }
            })
            // Search global untuk semua kolom
            ->filter(function ($q) use ($req) {
                if ($search = $req->input('search.value')) {
                    $q->where(function ($sub) use ($search) {
                        $sub->where('name', 'like', "%{$search}%")
                            ->orWhere('value', 'like', "%{$search}%")
                            ->orWhere('description', 'like', "%{$search}%");
                    });
                }
            })
            ->rawColumns(['action', 'type', 'is_editable'])
            ->toJson();
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.configurations.create', [
            'types' => [
                'string' => 'String',
                'integer' => 'Integer',
                'boolean' => 'Boolean',
                'json' => 'JSON',
            ],
            'groups' => [
                'system' => 'System',
                'academic' => 'Academic',
                'notification' => 'Notification',
                'general' => 'General',
            ],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(ConfigurationStoreRequest $request)
    {
        // Mengambil data yang sudah divalidasi dari request.
        $data = $request->validated();

        // Memeriksa apakah konfigurasi dengan nama yang sama sudah ada.
        $configurationExists = Configuration::where('name', $data['name'])->exists();

        if ($configurationExists) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Konfigurasi dengan nama yang sama sudah ada.');
        }

        // Buat konfigurasi baru.
        Configuration::create($data);

        return redirect()->route('admin.configurations.index')
            ->with('success', 'Konfigurasi berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        return view('admin.configurations.edit', [
            'configuration' => Configuration::findOrFail($id),
            'types' => [
                'string' => 'String',
                'integer' => 'Integer',
                'boolean' => 'Boolean',
                'json' => 'JSON',
            ],
            'groups' => [
                'system' => 'System',
                'academic' => 'Academic',
                'notification' => 'Notification',
                'general' => 'General',
            ],
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ConfigurationUpdateRequest $request, string $id)
    {
        // Mengambil data yang sudah divalidasi dari request.
        $data = $request->validated();

        // Mencari data konfigurasi berdasarkan ID.
        $configuration = Configuration::findOrFail($id);

        // Memeriksa apakah konfigurasi dengan nama yang sama sudah ada,
        // tetapi mengecualikan konfigurasi yang sedang di-update.
        $configurationExists = Configuration::where('name', $data['name'])
            ->where('id', '!=', $id)
            ->exists();

        // Jika data duplikat ditemukan, kembalikan dengan pesan error.
        if ($configurationExists) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Konfigurasi dengan nama yang sama sudah ada.');
        }

        // Lakukan pembaruan data.
        $configuration->update($data);

        return redirect()->route('admin.configurations.index')
            ->with('success', 'Konfigurasi berhasil diubah.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $configuration = Configuration::findOrFail($id);

        // Jika konfigurasi tidak dapat diedit, maka tidak boleh dihapus
        if (!$configuration->is_editable) {
            return response()->json([
                'message' => 'Konfigurasi yang tidak dapat diedit tidak boleh dihapus'
            ], 400);
        }

        // Hapus data konfigurasi.
        $configuration->delete();

        return response()->json([
            'message' => 'Konfigurasi berhasil dihapus.'
        ], 200);
    }
}
