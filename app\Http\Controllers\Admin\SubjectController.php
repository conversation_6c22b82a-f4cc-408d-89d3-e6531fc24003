<?php

namespace App\Http\Controllers\Admin;

use App\Models\Subject;
use App\Models\Program;
use App\Enums\ActivityStatusEnum;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Requests\Requests\SubjectRequests\SubjectStoreRequest;
use App\Http\Requests\Requests\SubjectRequests\SubjectUpdateRequest;

class SubjectController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->datatables($request);
        }

        return view('admin.subjects.index', [
            'programs' => Program::all(),
            'statuses' => ActivityStatusEnum::options(),
        ]);
    }

    public function datatables($req)
    {
        $query = Subject::with('program');

        return DataTables::eloquent($query)
            ->addIndexColumn()
            ->editColumn('program', function ($row) {
                return $row->program ? $row->program->name : '-';
            })
            ->editColumn('activity_status', function ($row) {
                return '<span class="badge bg-' . $row->activity_status->color() . ' text-white">' . $row->activity_status->label() . '</span>';
            })
            ->addColumn('action', function ($row) {
                return view('admin.subjects._action', [
                    'edit' => route('admin.subjects.edit', $row->id),
                    'destroy' => route('admin.subjects.destroy', $row->id),
                    'id' => $row->id,
                ]);
            })
            // Filter kolom program
            ->filterColumn('program', function ($q, $keyword) {
                if (!empty($keyword) && $keyword !== 'all') {
                    $q->whereHas('program', function ($query) use ($keyword) {
                        $query->where('id', $keyword);
                    });
                }
            })
            // Filter kolom status
            ->filterColumn('activity_status', function ($q, $keyword) {
                if (!empty($keyword) && $keyword !== 'all') {
                    $q->where('activity_status', $keyword);
                }
            })
            // Search global untuk semua kolom
            ->filter(function ($q) use ($req) {
                if ($search = $req->input('search.value')) {
                    $q->where(function ($sub) use ($search) {
                        $sub->where('name', 'like', "%{$search}%")
                            ->orWhere('code', 'like', "%{$search}%");
                    });
                }
            })
            ->rawColumns(['action', 'activity_status'])
            ->toJson();
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.subjects.create', [
            'programs' => Program::all(),
            'statuses' => ActivityStatusEnum::options(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(SubjectStoreRequest $request)
    {
        // Mengambil data yang sudah divalidasi dari request.
        $data = $request->validated();

        // Set default values
        $data['activity_status'] = $data['activity_status'] ?? ActivityStatusEnum::Active->value;

        // Buat mata pelajaran baru.
        Subject::create($data);

        return redirect()->route('admin.subjects.index')
            ->with('success', 'Mata pelajaran berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        return view('admin.subjects.edit', [
            'programs' => Program::all(),
            'statuses' => ActivityStatusEnum::options(),
            'subject' => Subject::findOrFail($id),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(SubjectUpdateRequest $request, string $id)
    {
        // Mengambil data yang sudah divalidasi dari request.
        $data = $request->validated();

        // Mencari data mata pelajaran berdasarkan ID.
        $subject = Subject::findOrFail($id);

        // Set default values
        $data['activity_status'] = $data['activity_status'] ?? ActivityStatusEnum::Active->value;

        // Lakukan pembaruan data.
        $subject->update($data);

        return redirect()->route('admin.subjects.index')
            ->with('success', 'Mata pelajaran berhasil diubah.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Hapus data mata pelajaran.
        Subject::findOrFail($id)->delete();

        return response()->json([
            'message' => 'Mata pelajaran berhasil dihapus.'
        ], 200);
    }
}
