<?php

use Illuminate\Support\Facades\Route;
use App\Helpers\ConfigurationHelper;

if (!function_exists('is_active')) {
    function is_active($patterns, string $return = '', bool $wildcard = false): string
    {
        $currentRoute = Route::currentRouteName();
        $currentPath = request()->path();

        foreach ((array) $patterns as $pattern) {
            // Match by route name
            if (
                $currentRoute && (
                    $currentRoute === $pattern ||
                    ($wildcard && str_starts_with($currentRoute, rtrim($pattern, '.')))
                )
            ) {
                return $return;
            }

            // Match by URL path
            if (
                $currentPath === ltrim($pattern, '/') ||
                ($wildcard && str_starts_with($currentPath, ltrim(rtrim($pattern, '*'), '/')))
            ) {
                return $return;
            }
        }

        return '';
    }
}

if (!function_exists('config_value')) {
    /**
     * Get configuration value by name
     */
    function config_value(string $name, $default = null)
    {
        return ConfigurationHelper::get($name, $default);
    }
}

if (!function_exists('config_typed')) {
    /**
     * Get configuration value with proper type casting
     */
    function config_typed(string $name, $default = null)
    {
        return ConfigurationHelper::getTyped($name, $default);
    }
}

if (!function_exists('config_set')) {
    /**
     * Set configuration value
     */
    function config_set(string $name, $value, string $type = 'string', string $group = null, string $description = null)
    {
        return ConfigurationHelper::set($name, $value, $type, $group, $description);
    }
}
