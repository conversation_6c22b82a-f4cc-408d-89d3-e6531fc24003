<?php

namespace App\Helpers;

use App\Models\Configuration;

class ConfigurationHelper
{
    /**
     * Get configuration value by name
     */
    public static function get(string $name, $default = null)
    {
        return Configuration::getValue($name, $default);
    }

    /**
     * Set configuration value by name
     */
    public static function set(string $name, $value, string $type = 'string', string $group = null, string $description = null)
    {
        return Configuration::setValue($name, $value, $type, $group, $description);
    }

    /**
     * Get multiple configuration values by group
     */
    public static function getGroup(string $group)
    {
        return Configuration::where('group', $group)->pluck('value', 'name')->toArray();
    }

    /**
     * Get all configurations as array
     */
    public static function all()
    {
        return Configuration::pluck('value', 'name')->toArray();
    }

    /**
     * Check if configuration exists
     */
    public static function has(string $name): bool
    {
        return Configuration::where('name', $name)->exists();
    }

    /**
     * Get configuration with type casting
     */
    public static function getTyped(string $name, $default = null)
    {
        $config = Configuration::where('name', $name)->first();

        if (!$config) {
            return $default;
        }

        switch ($config->type) {
            case 'boolean':
                return filter_var($config->value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $config->value;
            case 'json':
                return json_decode($config->value, true);
            default:
                return $config->value;
        }
    }

    /**
     * Get common system configurations
     */
    public static function getSystemConfigs()
    {
        return [
            'app_name' => self::get('app_name', 'Rawooh MVC'),
            'app_version' => self::get('app_version', '1.0.0'),
            'maintenance_mode' => self::getTyped('maintenance_mode', false),
        ];
    }

    /**
     * Get academic configurations
     */
    public static function getAcademicConfigs()
    {
        return [
            'max_students_per_class' => self::getTyped('max_students_per_class', 30),
            'academic_year_start_month' => self::getTyped('academic_year_start_month', 7),
            'attendance_tolerance_minutes' => self::getTyped('attendance_tolerance_minutes', 15),
        ];
    }

    /**
     * Get notification configurations
     */
    public static function getNotificationConfigs()
    {
        return [
            'enable_email_notifications' => self::getTyped('enable_email_notifications', true),
            'enable_sms_notifications' => self::getTyped('enable_sms_notifications', false),
        ];
    }

    /**
     * Get security configurations
     */
    public static function getSecurityConfigs()
    {
        return [
            'session_timeout_minutes' => self::getTyped('session_timeout_minutes', 120),
            'max_login_attempts' => self::getTyped('max_login_attempts', 5),
        ];
    }
}
