<?php

namespace App\Http\Requests\Requests\ClassroomRequests;

use App\Enums\ActivityStatusEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ClassroomUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $classroomId = $this->route('classroom');
        
        return [
            'code' => ['required', 'string', 'max:20', Rule::unique('classrooms', 'code')->ignore($classroomId)],
            'name' => ['required', 'string', 'max:255'],
            'level' => ['required', 'string', 'max:255'],
            'capacity' => ['required', 'integer', 'min:1', 'max:100'],
            'program_id' => ['required', 'exists:programs,id'],
            'shift_id' => ['nullable', 'exists:shifts,id'],
            'teacher_id' => ['nullable', 'exists:teachers,id'],
            'academic_year_id' => ['required', 'exists:academic_years,id'],
            'activity_status' => ['required', 'string', 'max:255', Rule::enum(ActivityStatusEnum::class)],
            'description' => ['nullable', 'string'],
        ];
    }

    public function messages(): array
    {
        return [
            'code.required' => 'Kode kelas harus diisi',
            'code.string' => 'Kode kelas harus berupa string',
            'code.max' => 'Kode kelas maksimal 20 karakter',
            'code.unique' => 'Kode kelas sudah digunakan',
            'name.required' => 'Nama kelas harus diisi',
            'name.string' => 'Nama kelas harus berupa string',
            'name.max' => 'Nama kelas maksimal 255 karakter',
            'level.required' => 'Tingkat kelas harus diisi',
            'level.string' => 'Tingkat kelas harus berupa string',
            'level.max' => 'Tingkat kelas maksimal 255 karakter',
            'capacity.required' => 'Kapasitas kelas harus diisi',
            'capacity.integer' => 'Kapasitas kelas harus berupa angka',
            'capacity.min' => 'Kapasitas kelas minimal 1',
            'capacity.max' => 'Kapasitas kelas maksimal 100',
            'program_id.required' => 'Program harus dipilih',
            'program_id.exists' => 'Program yang dipilih tidak valid',
            'shift_id.exists' => 'Shift yang dipilih tidak valid',
            'teacher_id.exists' => 'Wali kelas yang dipilih tidak valid',
            'academic_year_id.required' => 'Tahun akademik harus dipilih',
            'academic_year_id.exists' => 'Tahun akademik yang dipilih tidak valid',
            'activity_status.required' => 'Status aktivitas harus dipilih',
            'activity_status.string' => 'Status aktivitas harus berupa string',
            'activity_status.max' => 'Status aktivitas maksimal 255 karakter',
            'activity_status.enum' => 'Status aktivitas harus berupa aktif atau tidak aktif',
            'description.string' => 'Deskripsi harus berupa string',
        ];
    }
}
