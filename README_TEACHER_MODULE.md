# Modul Teacher

Modul Teacher adalah implementasi CRUD untuk manajemen data guru yang mengikuti pola yang sama dengan modul Student untuk konsistensi dalam aplikasi. Modul ini mendukung 2 role guru: **Subject Teacher** (<PERSON>) dan **Substitute Teacher** (<PERSON>) menggunakan Spatie Laravel Permission.

## Struktur File

### Models

-   `app/Models/Teacher.php` - Model Teacher dengan relasi ke User dan helper methods untuk teacher role

### Enums

-   `app/Enums/TeacherRoleEnum.php` - Enum untuk role guru (subject_teacher, substitute_teacher)

### Controllers

-   `app/Http/Controllers/Admin/TeacherController.php` - Controller untuk CRUD Teacher dengan support teacher roles

### Request Validation

-   `app/Http/Requests/Requests/TeacherRequests/TeacherStoreRequest.php` - Validasi untuk create dengan teacher_role
-   `app/Http/Requests/Requests/TeacherRequests/TeacherUpdateRequest.php` - <PERSON>idasi untuk update dengan teacher_role

### Views

-   `resources/views/admin/teachers/index.blade.php` - <PERSON>aman daftar guru
-   `resources/views/admin/teachers/create.blade.php` - Form tambah guru
-   `resources/views/admin/teachers/edit.blade.php` - Form edit guru
-   `resources/views/admin/teachers/_action.blade.php` - Action buttons untuk datatables

## Database Schema

Tabel `teachers` memiliki struktur:

-   `id` (primary key)
-   `birth_place` (string) - Tempat lahir
-   `birth_date` (date) - Tanggal lahir
-   `gender` (enum: 'male', 'female') - Jenis kelamin
-   `user_id` (foreign key) - Relasi ke tabel users
-   `timestamps`

## Relasi Database

### Teacher Model

-   `belongsTo(User::class)` - Setiap teacher memiliki satu user
-   `hasMany(Classroom::class)` - Teacher bisa menjadi wali kelas banyak classroom
-   `hasMany(TeacherAssignment::class)` - Teacher bisa memiliki banyak assignment
-   `hasMany(LeaveRequest::class)` - Teacher bisa memiliki banyak leave request
-   `hasMany(Journal::class)` - Teacher bisa memiliki banyak journal
-   `hasMany(StudentAttendance::class)` - Teacher bisa mencatat banyak student attendance
-   `hasMany(TeacherAttendance::class)` - Teacher bisa memiliki banyak teacher attendance

## Validasi

### TeacherStoreRequest

**User Data:**

-   `name` - required, string, max:255
-   `email` - required, email, unique:users
-   `username` - required, string, unique:users
-   `password` - required, string, min:8
-   `phone_number` - nullable, string, max:20
-   `status` - nullable, enum UserStatusEnum

**Teacher Data:**

-   `birth_place` - required, string, max:255
-   `birth_date` - required, date
-   `gender` - required, enum GenderEnum

### TeacherUpdateRequest

Sama dengan TeacherStoreRequest, kecuali:

-   `password` - nullable (tidak wajib saat update)
-   `email` dan `username` - unique dengan ignore current user

## Fitur

### Index Page

-   DataTables dengan server-side processing
-   Filter berdasarkan jenis kelamin dan status
-   Search functionality
-   Pagination
-   Action buttons (Edit, Delete)

### Create/Edit Form

-   Form validation dengan error handling
-   Password toggle visibility
-   Date picker untuk tanggal lahir
-   Dropdown untuk jenis kelamin dan status
-   Consistent UI/UX dengan modul student

### CRUD Operations

-   **Create**: Membuat user baru dengan role 'teacher' dan data teacher terkait
-   **Read**: Menampilkan daftar guru dengan datatables
-   **Update**: Update data user dan teacher dalam transaction
-   **Delete**: Soft delete dengan konfirmasi SweetAlert

## Enum yang Digunakan

-   `GenderEnum` - Untuk jenis kelamin (Male, Female)
-   `UserStatusEnum` - Untuk status user (Active, Inactive)
-   `TeacherRoleEnum` - Untuk role guru (SubjectTeacher, SubstituteTeacher)

## Routes

```php
Route::resource('teachers', TeacherController::class);
```

Routes yang tersedia:

-   `GET admin/teachers` - Index (daftar guru)
-   `GET admin/teachers/create` - Form create
-   `POST admin/teachers` - Store guru baru
-   `GET admin/teachers/{teacher}` - Show detail guru
-   `GET admin/teachers/{teacher}/edit` - Form edit
-   `PUT admin/teachers/{teacher}` - Update guru
-   `DELETE admin/teachers/{teacher}` - Delete guru

## Konsistensi dengan Modul Student

Modul Teacher mengikuti pola yang sama dengan modul Student:

1. **Struktur File**: Sama dengan student (Controller, Request, Views)
2. **Naming Convention**: Konsisten dengan pattern yang ada
3. **Validasi**: Menggunakan pola validasi yang sama
4. **Error Handling**: Menggunakan transaction dan error handling yang sama
5. **UI/UX**: Menggunakan komponen dan styling yang sama
6. **Database Transaction**: Menggunakan DB::transaction untuk operasi yang melibatkan multiple tables

## Perbaikan yang Dilakukan

1. **Enum Consistency**: Memperbaiki penggunaan `Gender` menjadi `GenderEnum` di model Teacher untuk konsistensi dengan Student
2. **Request Validation**: Membuat TeacherStoreRequest dan TeacherUpdateRequest yang konsisten dengan StudentRequest
3. **Controller Pattern**: Mengikuti pola yang sama dengan StudentController
4. **Views Structure**: Menggunakan struktur dan styling yang sama dengan views Student
5. **Routes Configuration**: Menambahkan resource routes yang konsisten

## Testing

Semua file telah divalidasi:

-   ✅ No syntax errors dalam model Teacher
-   ✅ No syntax errors dalam TeacherController
-   ✅ Routes terdaftar dengan benar
-   ✅ Configuration cache berhasil
-   ✅ Struktur file konsisten dengan modul Student

## Teacher Roles dengan Spatie Permission

### Role yang Tersedia

1. **subject_teacher** (Guru Mata Pelajaran)

    - Guru yang mengajar mata pelajaran tertentu
    - Memiliki assignment ke subject dan classroom
    - Dapat membuat journal dan mencatat attendance

2. **substitute_teacher** (Guru Pengganti)
    - Guru yang dapat menggantikan guru lain
    - Dapat mengajar berbagai mata pelajaran
    - Fleksibel untuk substitution records

### Implementasi Role Management

-   Menggunakan Spatie Laravel Permission untuk role management
-   Role disimpan di tabel `model_has_roles` (Spatie)
-   Tidak ada kolom tambahan di tabel `teachers`
-   Role dapat diubah melalui form edit teacher

### Helper Methods di Model Teacher

```php
// Mendapatkan teacher role sebagai enum
$teacher->teacher_role; // Returns TeacherRoleEnum or null

// Mendapatkan label role
$teacher->teacher_role_label; // Returns string label

// Cek apakah teacher memiliki role tertentu
$teacher->hasTeacherRole('subject_teacher'); // Returns boolean
```

## Penggunaan

1. Akses halaman guru melalui menu admin
2. Tambah guru baru dengan mengisi form yang tersedia (termasuk memilih role)
3. Edit data guru melalui action button (dapat mengubah role)
4. Hapus guru dengan konfirmasi
5. Filter dan search data guru berdasarkan role, gender, status, dll.
