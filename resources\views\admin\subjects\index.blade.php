@extends('admin.layouts.app')

@section('title', 'Mata Pelajaran')

@section('content')
    @include('admin.components.page-title', ['title' => 'Mata Pelajaran', 'breadcrumb' => 'Mata Pelajaran'])

    <div class="row">
        <div class="col-lg-12">
            <div class="card" id="tasksList">
                <!-- Start Header -->
                <div class="card-header border-0">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">Mata Pelajaran
                            <span class="badge bg-primary-subtle text-primary ms-2" id="total-subjects">0</span>
                        </h5>
                        <div class="flex-shrink-0">
                            <div class="d-flex flex-wrap gap-2">
                                <a href="{{ route('admin.subjects.create') }}" class="btn btn-primary">
                                    <i class="ri-add-line align-bottom me-1"></i> Tambah Mata Pelajaran
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- End Header -->

                <!-- Start Filter Section -->
                <div class="card-body border border-dashed border-end-0 border-start-0">
                    <form id="filter-form" class="row g-3">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="program-filter" class="form-label">Program</label>
                                <select class="form-select" data-choices name="program" id="filter_program">
                                    <option value="">Semua Program</option>
                                    @foreach ($programs as $program)
                                        <option value="{{ $program->id }}">{{ $program->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="status-filter" class="form-label">Status</label>
                                <select class="form-select" data-choices name="activity_status" id="filter_status">
                                    <option value="">Semua Status</option>
                                    @foreach ($statuses as $value => $label)
                                        <option value="{{ $value }}">{{ $label }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <!-- Search Input -->
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="search-input" class="form-label">Cari</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Cari Kode, Nama, atau Deskripsi" id="search-input" name="search">
                                    <button class="btn btn-primary" type="button" id="search-button">
                                        <i class="ri-search-line"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- End Filter Section -->

                <!-- Start Table Section -->
                <div class="card-body">
                    <div class="table-responsive table-card">
                        <table id="subjects-table" class="table align-middle table-nowrap" style="width: 100%;">
                            <thead class="table-light text-muted">
                                <tr>
                                    <th scope="col" style="width: 65px;">
                                        No
                                    </th>
                                    <th>Kode</th>
                                    <th>Nama Mata Pelajaran</th>
                                    <th>Program</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                <!-- End Table Section -->

            </div>
            <!--end card-->
        </div>
        <!--end col-->
    </div>
    <!--end row-->
@endsection

@include('admin.partials.plugins._jquery')
@include('admin.partials.plugins._datatables')

@push('scripts')
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            var table = $('#subjects-table').DataTable({
                lengthChange: false,
                searching: false,
                ordering: false,
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('admin.subjects.index') }}",
                    data: function(d) {
                        d.columns[3].search.value = $('#filter_program').val(); // program
                        d.columns[4].search.value = $('#filter_status').val(); // activity_status
                        d.search.value = $('#search-input').val(); // search
                    },
                    complete: response => {
                        const totalRecords = response.responseJSON?.recordsTotal || 0;
                        $('#total-subjects').text(totalRecords);
                    },
                    error: xhr => {
                        console.error('Error:', xhr.responseJSON.message);
                    },
                },
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'code',
                        name: 'code'
                    },
                    {
                        data: 'name',
                        name: 'name'
                    },
                    {
                        data: 'program',
                        name: 'program'
                    },
                    {
                        data: 'activity_status',
                        name: 'activity_status'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false
                    }
                ],
                language: {
                    processing: '<div class="spinner-border text-primary m-1" role="status"></div>',
                    searchPlaceholder: "Cari...",
                    lengthMenu: "Tampilkan _MENU_ data",
                    zeroRecords: "Data tidak ditemukan",
                    info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                    infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                    infoFiltered: "(disaring dari _MAX_ total data)",
                    paginate: {
                        first: "Pertama",
                        last: "Terakhir",
                        next: "Selanjutnya",
                        previous: "Sebelumnya",
                    }
                },
            });

            // Filter functionality
            $('#filter_program, #filter_status').on('change', function() {
                table.draw();
            });

            // Search functionality
            $('#search-button').on('click', function() {
                table.draw();
            });

            // Menyesuaikan kolom tabel saat sidebar di-toggle
            $('#topnav-hamburger-icon').click(() => {
                setTimeout(() => {
                    table.columns.adjust().draw();
                }, 300);
            });

            // Delete button
            table.on('click', '.btn-delete-item', function() {
                const url = $(this).data('url');
                Swal.fire({
                    title: 'Apakah Anda yakin ingin menghapus data ini?',
                    text: 'Data yang dihapus tidak dapat dikembalikan!',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Ya, hapus!',
                    cancelButtonText: 'Batal',
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: url,
                            type: 'DELETE',
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                table.draw();
                                Swal.fire({
                                    title: 'Berhasil!',
                                    text: response.message || 'Data berhasil dihapus!',
                                    icon: 'success',
                                    showConfirmButton: true,
                                });
                            },
                            error: function(xhr, status, error) {
                                Swal.fire({
                                    title: 'Gagal!',
                                    text: xhr.responseJSON?.message || 'Data gagal dihapus!',
                                    showConfirmButton: true,
                                    icon: 'error',
                                });
                            }
                        });
                    }
                });
            });
        });
    </script>
@endpush
