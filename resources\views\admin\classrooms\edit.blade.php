@extends('admin.layouts.app')

@section('title', '<PERSON> Kelas')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Edit Kelas',
        'breadcrumb' => 'Manajemen Akademik',
    ])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">Edit <PERSON></h5>
                    </div>
                </div>

                <div class="card-body">
                    <form id="edit-form" method="POST" action="{{ route('admin.classrooms.update', $classroom->id) }}">
                        @csrf
                        @method('PUT')

                        <div class="row g-3">
                            <!-- <PERSON>de <PERSON> -->
                            <div class="col-md-6">
                                <label for="code" class="form-label"><PERSON><PERSON> <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('code') is-invalid @enderror" id="code" name="code"
                                       value="{{ old('code', $classroom->code) }}" placeholder="contoh: X-IPA-1" required>
                                @error('code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Nama Kelas -->
                            <div class="col-md-6">
                                <label for="name" class="form-label">Nama Kelas <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name"
                                       value="{{ old('name', $classroom->name) }}" placeholder="contoh: Kelas X IPA 1" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Tingkat -->
                            <div class="col-md-6">
                                <label for="level" class="form-label">Tingkat <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('level') is-invalid @enderror" id="level" name="level"
                                       value="{{ old('level', $classroom->level) }}" placeholder="contoh: X, XI, XII" required>
                                @error('level')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Kapasitas -->
                            <div class="col-md-6">
                                <label for="capacity" class="form-label">Kapasitas <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('capacity') is-invalid @enderror" id="capacity" name="capacity"
                                       value="{{ old('capacity', $classroom->capacity) }}" placeholder="contoh: 30" min="1" max="100" required>
                                @error('capacity')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Program -->
                            <div class="col-md-6">
                                <label for="program_id" class="form-label">Program <span class="text-danger">*</span></label>
                                <select class="form-select @error('program_id') is-invalid @enderror" data-choices id="program_id" name="program_id" required>
                                    <option value="" disabled>Pilih Program</option>
                                    @foreach ($programs as $program)
                                        <option value="{{ $program->id }}" {{ old('program_id', $classroom->program_id) == $program->id ? 'selected' : '' }}>
                                            {{ $program->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('program_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Shift -->
                            <div class="col-md-6">
                                <label for="shift_id" class="form-label">Shift</label>
                                <select class="form-select @error('shift_id') is-invalid @enderror" data-choices id="shift_id" name="shift_id">
                                    <option value="">Pilih Shift (Opsional)</option>
                                    @foreach ($shifts as $shift)
                                        <option value="{{ $shift->id }}" {{ old('shift_id', $classroom->shift_id) == $shift->id ? 'selected' : '' }}>
                                            {{ $shift->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('shift_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Wali Kelas -->
                            <div class="col-md-6">
                                <label for="teacher_id" class="form-label">Wali Kelas</label>
                                <select class="form-select @error('teacher_id') is-invalid @enderror" data-choices id="teacher_id" name="teacher_id">
                                    <option value="">Pilih Wali Kelas (Opsional)</option>
                                    @foreach ($teachers as $teacher)
                                        <option value="{{ $teacher->id }}" {{ old('teacher_id', $classroom->teacher_id) == $teacher->id ? 'selected' : '' }}>
                                            {{ $teacher->user->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('teacher_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Tahun Akademik -->
                            <div class="col-md-6">
                                <label for="academic_year_id" class="form-label">Tahun Akademik <span class="text-danger">*</span></label>
                                <select class="form-select @error('academic_year_id') is-invalid @enderror" data-choices id="academic_year_id" name="academic_year_id" required>
                                    <option value="" disabled>Pilih Tahun Akademik</option>
                                    @foreach ($academicYears as $academicYear)
                                        <option value="{{ $academicYear->id }}" {{ old('academic_year_id', $classroom->academic_year_id) == $academicYear->id ? 'selected' : '' }}>
                                            {{ $academicYear->name }} - {{ $academicYear->semester->label() }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('academic_year_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Status -->
                            <div class="col-md-6">
                                <label for="activity_status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select @error('activity_status') is-invalid @enderror" data-choices id="activity_status" name="activity_status" required>
                                    <option value="" disabled>Pilih Status</option>
                                    @foreach ($statuses as $value => $label)
                                        <option value="{{ $value }}" {{ old('activity_status', $classroom->activity_status->value) == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('activity_status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Deskripsi -->
                            <div class="col-12">
                                <label for="description" class="form-label">Deskripsi</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="3" placeholder="Deskripsi kelas (opsional)">{{ old('description', $classroom->description) }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                        </div>

                        <!-- Tombol Aksi -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="hstack justify-content-end gap-2">
                                    <a href="{{ route('admin.classrooms.index') }}" class="btn btn-ghost-secondary">
                                        <i class="ri-close-line align-bottom"></i> Batal
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="submit-btn">
                                        <i class="ri-save-line align-bottom"></i> Perbarui
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
