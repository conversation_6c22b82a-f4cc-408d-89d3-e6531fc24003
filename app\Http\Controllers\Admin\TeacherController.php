<?php

namespace App\Http\Controllers\Admin;

use App\Enums\GenderEnum;
use App\Enums\UserStatusEnum;
use App\Enums\TeacherRoleEnum;
use App\Models\Teacher;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Requests\Requests\TeacherRequests\TeacherStoreRequest;
use App\Http\Requests\Requests\TeacherRequests\TeacherUpdateRequest;

class TeacherController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->datatables($request);
        }

        return view('admin.teachers.index', [
            'genders' => GenderEnum::options(),
            'statuses' => UserStatusEnum::options(),
            'teacherRoles' => TeacherRoleEnum::options(),
        ]);
    }

    public function datatables($req)
    {
        $query = Teacher::with(['user'])->select('teachers.*');

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('name', function ($row) {
                return $row->user->name ?? '-';
            })
            ->addColumn('email', function ($row) {
                return $row->user->email ?? '-';
            })
            ->addColumn('phone_number', function ($row) {
                return $row->user->phone_number ?? '<span class="text-muted">-</span>';
            })
            ->addColumn('gender', function ($row) {
                $color = $row->gender->color();
                return '<span class="badge bg-' . $color . '-subtle text-' . $color . '">' . $row->gender->label() . '</span>';
            })
            ->addColumn('status', function ($row) {
                $status = $row->user->status ?? null;
                if ($status) {
                    $color = $status->color();
                    return '<span class="badge bg-' . $color . '-subtle text-' . $color . '">' . $status->label() . '</span>';
                }
                return '<span class="text-muted">-</span>';
            })
            ->addColumn('teacher_role', function ($row) {
                $role = $row->teacher_role;
                if ($role) {
                    $color = $role->color();
                    return '<span class="badge bg-' . $color . '-subtle text-' . $color . '">' . $role->label() . '</span>';
                }
                return '<span class="text-muted">-</span>';
            })
            ->addColumn('action', function ($row) {
                return view('admin.teachers._action', [
                    'edit' => route('admin.teachers.edit', $row->id),
                    'destroy' => route('admin.teachers.destroy', $row->id),
                    'id' => $row->id,
                ]);
            })
            // Filter kolom gender
            ->filterColumn('gender', function ($q, $keyword) {
                if (!empty($keyword) && $keyword !== 'all') {
                    $q->where('gender', $keyword);
                }
            })
            // Filter kolom status
            ->filterColumn('status', function ($q, $keyword) {
                if (!empty($keyword) && $keyword !== 'all') {
                    $q->whereHas('user', function ($query) use ($keyword) {
                        $query->where('status', $keyword);
                    });
                }
            })
            // Filter kolom teacher_role
            ->filterColumn('teacher_role', function ($q, $keyword) {
                if (!empty($keyword) && $keyword !== 'all') {
                    $q->whereHas('user.roles', function ($query) use ($keyword) {
                        $query->where('name', $keyword);
                    });
                }
            })
            ->rawColumns(['gender', 'status', 'teacher_role', 'action', 'phone_number'])
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.teachers.create', [
            'genders' => GenderEnum::options(),
            'statuses' => UserStatusEnum::options(),
            'teacherRoles' => TeacherRoleEnum::options(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(TeacherStoreRequest $request)
    {
        // Mengambil data yang sudah divalidasi dari request.
        $data = $request->validated();

        // Format tanggal untuk birth_date.
        if (isset($data['birth_date'])) {
            $data['birth_date'] = Carbon::parse($data['birth_date'])->toDateString();
        }

        // Memisahkan data user dan teacher
        $userData = [
            'name' => $data['name'],
            'email' => $data['email'],
            'phone_number' => $data['phone_number'],
            'username' => $data['username'],
            'password' => bcrypt($data['password']),
            'status' => $data['status'] ?? UserStatusEnum::Active->value,
        ];

        $teacherData = [
            'birth_place' => $data['birth_place'],
            'birth_date' => $data['birth_date'],
            'gender' => $data['gender'],
        ];

        // Menggunakan transaction untuk memastikan kedua operasi (user dan teacher) berhasil
        DB::transaction(function () use ($userData, $teacherData, $data) {
            // Buat user baru
            $user = User::create($userData);

            // Assign role guru berdasarkan teacher_role yang dipilih
            $user->assignRole($data['teacher_role']);

            // Buat data guru terkait user
            $user->teacher()->create($teacherData);
        });

        return redirect()->route('admin.teachers.index')
            ->with('success', 'Data guru berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $teacher = Teacher::with('user')->findOrFail($id);

        return view('admin.teachers.edit', [
            'teacher' => $teacher,
            'genders' => GenderEnum::options(),
            'statuses' => UserStatusEnum::options(),
            'teacherRoles' => TeacherRoleEnum::options(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(TeacherUpdateRequest $request, string $id)
    {
        // Mengambil data yang sudah divalidasi dari request.
        $data = $request->validated();

        // Mencari data guru berdasarkan ID.
        $teacher = Teacher::with('user')->findOrFail($id);

        // Format tanggal untuk birth_date.
        if (isset($data['birth_date'])) {
            $data['birth_date'] = Carbon::parse($data['birth_date'])->toDateString();
        }

        // Memisahkan data user dan teacher
        $userData = [
            'name' => $data['name'],
            'email' => $data['email'],
            'phone_number' => $data['phone_number'],
            'username' => $data['username'],
            'status' => $data['status'] ?? $teacher->user->status->value,
        ];

        // Jika password diisi, tambahkan ke userData
        if (!empty($data['password'])) {
            $userData['password'] = bcrypt($data['password']);
        }

        $teacherData = [
            'birth_place' => $data['birth_place'],
            'birth_date' => $data['birth_date'],
            'gender' => $data['gender'],
        ];

        // Menggunakan transaction untuk memastikan kedua operasi berhasil
        DB::transaction(function () use ($teacher, $userData, $teacherData, $data) {
            // Update data user
            $teacher->user->update($userData);

            // Update role guru jika berubah
            if (isset($data['teacher_role'])) {
                $teacher->user->syncRoles($data['teacher_role']);
            }

            // Update data guru
            $teacher->update($teacherData);
        });

        return redirect()->route('admin.teachers.index')
            ->with('success', 'Data guru berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $teacher = Teacher::with('user')->findOrFail($id);

            DB::transaction(function () use ($teacher) {
                // Hapus data guru (akan otomatis menghapus user karena cascade)
                $teacher->delete();
            });

            return response()->json([
                'success' => true,
                'message' => 'Data guru berhasil dihapus.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus data guru: ' . $e->getMessage()
            ], 500);
        }
    }
}
