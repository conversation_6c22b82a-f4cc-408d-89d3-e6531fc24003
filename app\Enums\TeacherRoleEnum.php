<?php

namespace App\Enums;

enum TeacherRoleEnum: string
{
    case SubjectTeacher = 'subject_teacher';
    case SubstituteTeacher = 'substitute_teacher';

    public function label(): string
    {
        return match ($this) {
            self::SubjectTeacher => '<PERSON>',
            self::SubstituteTeacher => 'Guru <PERSON>',
        };
    }

    public static function options(): array
    {
        return [
            self::SubjectTeacher->value => self::SubjectTeacher->label(),
            self::SubstituteTeacher->value => self::SubstituteTeacher->label(),
        ];
    }

    public function color(): string
    {
        return match ($this) {
            self::SubjectTeacher => 'primary',
            self::SubstituteTeacher => 'secondary',
        };
    }

    public function description(): string
    {
        return match ($this) {
            self::SubjectTeacher => 'Guru yang mengajar mata pelajaran tertentu',
            self::SubstituteTeacher => 'Guru yang dapat menggantikan guru lain',
        };
    }
}
