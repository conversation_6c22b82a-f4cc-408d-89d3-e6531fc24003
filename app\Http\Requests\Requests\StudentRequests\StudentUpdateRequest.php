<?php

namespace App\Http\Requests\Requests\StudentRequests;

use App\Enums\GenderEnum;
use App\Enums\UserStatusEnum;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class StudentUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $student = $this->route('student');
        $userId = $student ? $student->user_id : null;

        return [
            // User data
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($userId)],
            'username' => ['required', 'string', 'max:255', Rule::unique('users')->ignore($userId)],
            'password' => ['nullable', 'string', 'min:8'],
            'phone_number' => ['nullable', 'string', 'max:20'],
            'status' => ['nullable', 'string', Rule::enum(UserStatusEnum::class)],

            // Student data
            'nis' => ['required', 'string', 'max:50', Rule::unique('students')->ignore($student)],
            'nisn' => ['required', 'string', 'max:50', Rule::unique('students')->ignore($student)],
            'birth_place' => ['required', 'string', 'max:255'],
            'birth_date' => ['required', 'date'],
            'gender' => ['required', 'string', Rule::enum(GenderEnum::class)],
            'parent_name' => ['required', 'string', 'max:255'],
            'parent_phone' => ['required', 'string', 'max:20'],
        ];
    }

    public function messages(): array
    {
        return [
            // User data
            'name.required' => 'Nama siswa harus diisi',
            'name.string' => 'Nama siswa harus berupa teks',
            'name.max' => 'Nama siswa maksimal 255 karakter',
            'email.required' => 'Email harus diisi',
            'email.string' => 'Email harus berupa teks',
            'email.email' => 'Format email tidak valid',
            'email.max' => 'Email maksimal 255 karakter',
            'email.unique' => 'Email sudah digunakan',
            'username.required' => 'Username harus diisi',
            'username.string' => 'Username harus berupa teks',
            'username.max' => 'Username maksimal 255 karakter',
            'username.unique' => 'Username sudah digunakan',
            'password.string' => 'Password harus berupa teks',
            'password.min' => 'Password minimal 8 karakter',
            'phone_number.string' => 'Nomor telepon harus berupa teks',
            'phone_number.max' => 'Nomor telepon maksimal 20 karakter',
            'status.string' => 'Status harus berupa teks',
            'status.enum' => 'Status tidak valid',

            // Student data
            'nis.required' => 'NIS harus diisi',
            'nis.string' => 'NIS harus berupa teks',
            'nis.max' => 'NIS maksimal 50 karakter',
            'nis.unique' => 'NIS sudah digunakan',
            'nisn.required' => 'NISN harus diisi',
            'nisn.string' => 'NISN harus berupa teks',
            'nisn.max' => 'NISN maksimal 50 karakter',
            'nisn.unique' => 'NISN sudah digunakan',
            'birth_place.required' => 'Tempat lahir harus diisi',
            'birth_place.string' => 'Tempat lahir harus berupa teks',
            'birth_place.max' => 'Tempat lahir maksimal 255 karakter',
            'birth_date.required' => 'Tanggal lahir harus diisi',
            'birth_date.date' => 'Tanggal lahir harus berupa tanggal',
            'gender.required' => 'Jenis kelamin harus diisi',
            'gender.string' => 'Jenis kelamin harus berupa teks',
            'gender.enum' => 'Jenis kelamin tidak valid',
            'parent_name.required' => 'Nama orang tua harus diisi',
            'parent_name.string' => 'Nama orang tua harus berupa teks',
            'parent_name.max' => 'Nama orang tua maksimal 255 karakter',
            'parent_phone.required' => 'Nomor telepon orang tua harus diisi',
            'parent_phone.string' => 'Nomor telepon orang tua harus berupa teks',
            'parent_phone.max' => 'Nomor telepon orang tua maksimal 20 karakter',
        ];
    }
}
